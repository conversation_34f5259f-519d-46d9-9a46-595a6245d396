import type { AxiosRequestConfig } from 'axios';

import axios from 'axios';

import { CONFIG } from 'src/config-global';

// ----------------------------------------------------------------------

const axiosInstance = axios.create({ baseURL: CONFIG.site.WorkforcesServerUrl });

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log('erorr', error);
    Promise.reject((error.response && error.response.data) || 'Something went wrong!');
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args];

    const res = await axiosInstance.get(url, { ...config });

    return res.data;
  } catch (error) {
    console.error('Failed to fetch:', error);
    throw error;
  }
};

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/basic',
    signUp: '/auth/register',
    refreshToken: '/auth/refresh-token',
    resetPassword: '/auth/reset-password',
  },
  user: {
    me: '/users/me',
  },
};
